.flight-review-travell-form-div{
    h2{
        font-size: 19px;
        font-weight: 500;
        color: #000;
        margin-bottom: 15px;
    }
    .body-div-data{
        background: #fff;
        border: 1px solid #d0d0d0;
        border-radius: 8px;
        margin-bottom: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);

        .body-padding-div{
            padding: 15px 20px;

            .passengers-container{
                display: flex;
                flex-direction: column;
                gap: 20px;
            }

            .passenger-section{
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                overflow: hidden;
                background: #fafafa;

                .section-header{
                    padding: 15px 20px;
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    border-bottom: 1px solid #e0e0e0;
                    cursor: pointer;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    transition: all 0.3s ease;

                    &:hover{
                        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
                    }

                    .section-title{
                        h3{
                            font-size: 16px;
                            font-weight: 600;
                            color: #2c3e50;
                            margin: 0 0 4px 0;
                            text-transform: uppercase;
                            letter-spacing: 0.5px;
                        }

                        .section-description{
                            font-size: 12px;
                            color: #6c757d;
                            margin: 0;
                            font-style: italic;
                        }
                    }

                    .expand-icon{
                        color: #495057;
                        transition: transform 0.3s ease;

                        svg{
                            font-size: 24px;
                        }
                    }
                }

                .section-content{
                    padding: 20px;
                    background: #fff;
                    border-top: 1px solid #f0f0f0;

                    .passenger-form{
                        margin-bottom: 25px;
                        padding: 20px;
                        border: 1px solid #e9ecef;
                        border-radius: 8px;
                        background: #ffffff;
                        box-shadow: 0 1px 3px rgba(0,0,0,0.05);

                        &:last-child{
                            margin-bottom: 0;
                        }

                        .passenger-title{
                            font-size: 14px;
                            font-weight: 600;
                            color: #495057;
                            margin: 0 0 15px 0;
                            text-transform: uppercase;
                            letter-spacing: 0.5px;
                            padding-bottom: 8px;
                            border-bottom: 2px solid #e9ecef;
                        }
                    }
                }
            }

            .pax-travel-div{
                display: flex;
                flex-direction: column;
                gap: 15px;
                h3{
                    font-size: 14px;
                    font-weight: 500;
                    text-transform: uppercase;
                    color: #495057;
                    margin-bottom: 10px;
                }
                .form-input-div{
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    flex-wrap: wrap;

                    .form-width-20{
                        width: calc(20% - 12px);
                    }
                    .form-width-25{
                        width: calc(25% - 12px);
                    }
                    .form-width-30{
                        width: calc(30% - 12px);
                    }
                    .form-width-40{
                        width: calc(40% - 12px);
                    }
                    .form-width-50{
                        width: calc(50% - 12px);
                    }
                }
            }
        }
    }

    .payment-bttn-div{
        display: flex;
        align-items: center;
        justify-content: end;
        padding: 15px 0;

        button{
            padding: 12px 30px;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
    }
}

@media only screen and (max-width: 768px) {
    .flight-review-travell-form-div{
        h2{
            font-size: 17px;
        }
        .body-div-data{
            margin-bottom: 10px;
            border-radius: 6px;

            .body-padding-div{
                padding: 12px 15px;

                .passengers-container{
                    gap: 15px;
                }

                .passenger-section{
                    .section-header{
                        padding: 12px 15px;

                        .section-title{
                            h3{
                                font-size: 14px;
                            }
                            .section-description{
                                font-size: 11px;
                            }
                        }

                        .expand-icon{
                            svg{
                                font-size: 20px;
                            }
                        }
                    }

                    .section-content{
                        padding: 15px;

                        .passenger-form{
                            padding: 15px;
                            margin-bottom: 15px;

                            .passenger-title{
                                font-size: 12px;
                                margin-bottom: 12px;
                            }
                        }
                    }
                }

                .pax-travel-div{
                    gap: 12px;
                    h3{
                        font-size: 12px;
                    }
                    .form-input-div{
                        gap: 10px;
                        flex-direction: column;

                        .form-width-20,
                        .form-width-25,
                        .form-width-30,
                        .form-width-40,
                        .form-width-50{
                            width: 100%;
                        }
                    }
                }
            }
        }

        .payment-bttn-div{
            padding: 12px 0;

            button{
                padding: 10px 25px;
                font-size: 14px;
            }
        }
    }
}
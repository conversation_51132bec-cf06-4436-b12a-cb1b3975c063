import React, { useEffect, useState } from 'react';
import { SubmitHandler, useFieldArray, useForm } from 'react-hook-form';
import { TravellerCheckList } from 'models/traveller-form-model';
import styles from './TravelForm.module.scss';
import { FormSearch } from 'models/flight-search-model';
import { Loader } from 'components/loader/loader';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';

interface FlightReviewTravellerFormProps {
  handleMakePayment: (travelForm:travel_form) => void;
  travellerCheckList: TravellerCheckList;
  travellerForm: any;
  pax: number[]
}

export interface travel_form{
  email:string
  phone:string
  phone_code:string
  travellers:Passenger[]
}

export interface Passenger {
  DOB: string;
  FName: string;
  LName: string;
  Nationality: string;
  PDOE: string; // Passport Date of Expiry
  PDOI: string; // Passport Date of Issue
  PLI: string;  // Place of Issue (if applicable)
  Pancard: string;
  PassportNo: string;
  Title: string;
  VisaType: string;
  paxHead: string; // Example: "Adult 1"
  paxType: "A" | "C" | "I"; // A = Adult, C = Child, I = Infant
}


const FlightReviewTravellerForm: React.FC<FlightReviewTravellerFormProps> = ({ travellerForm , handleMakePayment }) => {

  const [expandedSections, setExpandedSections] = useState<{[key: string]: boolean}>({
    adults: true,
    children: true,
    infants: true
  });

  const {
    register,
    control,
    handleSubmit,
    reset,
  } = useForm<travel_form>();

  const { fields } = useFieldArray({
    control,
    name: 'travellers',
  });

  const [isLoad, setIsLoad] = useState<boolean>(false);

  // Toggle section expansion
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Group passengers by type
  const groupedPassengers = React.useMemo(() => {
    const adults: Array<{passenger: Passenger, index: number}> = [];
    const children: Array<{passenger: Passenger, index: number}> = [];
    const infants: Array<{passenger: Passenger, index: number}> = [];

    fields.forEach((passenger: Passenger, index: number) => {
      if (passenger.paxType === 'A') {
        adults.push({passenger, index});
      } else if (passenger.paxType === 'C') {
        children.push({passenger, index});
      } else if (passenger.paxType === 'I') {
        infants.push({passenger, index});
      }
    });

    return { adults, children, infants };
  }, [fields]);

  useEffect(()=>{
    if (travellerForm) {
      reset(travellerForm); // Reset form with new default values
    }
  },[travellerForm,reset])


  useEffect(() => {
    const searchData = localStorage.getItem('dySearchSectorData')
    if (searchData) {
      const data = JSON.parse(searchData) as FormSearch

      const defaultPassengers: Passenger[] = []

      // Add Adult passengers
      for (let i = 0; i < data.travellers.adult; i++) {
        defaultPassengers.push({
          DOB: "",
          FName: "",
          LName: "",
          Nationality: "",
          PDOE: "",
          PDOI: "",
          PLI: "",
          Pancard: "",
          PassportNo: "",
          Title: "",
          VisaType: "",
          paxHead: `Adult ${i + 1}`,
          paxType: "A"
        })
      }

      // Add Child passengers
      for (let i = 0; i < data.travellers.child; i++) {
        defaultPassengers.push({
          DOB: "",
          FName: "",
          LName: "",
          Nationality: "",
          PDOE: "",
          PDOI: "",
          PLI: "",
          Pancard: "",
          PassportNo: "",
          Title: "",
          VisaType: "",
          paxHead: `Child ${i + 1}`,
          paxType: "C"
        })
      }

      // Add Infant passengers
      for (let i = 0; i < data.travellers.infant; i++) {
        defaultPassengers.push({
          DOB: "",
          FName: "",
          LName: "",
          Nationality: "",
          PDOE: "",
          PDOI: "",
          PLI: "",
          Pancard: "",
          PassportNo: "",
          Title: "",
          VisaType: "",
          paxHead: `Infant ${i + 1}`,
          paxType: "I"
        })
      }

      reset({
        email: travellerForm?.email || "",
        phone: travellerForm?.phone || "",
        phone_code: travellerForm?.phone_code || "",
        travellers: defaultPassengers
      })
    }
  }, [])

  // Helper function to render passenger form fields
  const renderPassengerForm = (passenger: Passenger, index: number) => (
    <div key={index} className={styles["passenger-form"]}>
      <h4 className={styles["passenger-title"]}>{passenger.paxHead}</h4>
      <div className={styles["form-input-div"]}>
        <div className={styles["form-width-20"]}>
          <div className="input-field">
            <select
              className="input-element"
              id={`travellers.${index}.Title`}
              {...register(`travellers.${index}.Title`, {
                required: "Please select a title.",
              })}
            >
              <option value="" disabled>
                Select Title
              </option>
              {passenger.paxType === "A" ? (
                <>
                  <option value="Mr">Mr</option>
                  <option value="Mrs">Mrs</option>
                  <option value="Ms">Ms</option>
                </>
              ) : passenger.paxType === "C" ? (
                <>
                  <option value="Master">Master</option>
                  <option value="Miss">Miss</option>
                </>
              ) : (
                <>
                  <option value="Master">Master</option>
                  <option value="Miss">Miss</option>
                </>
              )}
            </select>
            <label className="input-label">Title</label>
          </div>
        </div>

        <div className={styles["form-width-25"]}>
          <div className="input-field">
            <input
              className='input-element'
              id={`travellers.${index}.FName`}
              type="text"
              placeholder="Type..."
              {...register(`travellers.${index}.FName`, {
                required: "Please provide the first name.",
              })}
            />
            <label className='input-label'>First Name</label>
          </div>
        </div>

        <div className={styles["form-width-25"]}>
          <div className="input-field">
            <input
              className='input-element'
              id={`travellers.${index}.LName`}
              type="text"
              placeholder="Type..."
              {...register(`travellers.${index}.LName`, {
                required: "Please provide the last name.",
              })}
            />
            <label className='input-label'>Last Name</label>
          </div>
        </div>

        <div className={styles["form-width-30"]}>
          <div className="input-field">
            <input
              className='input-element'
              id={`travellers.${index}.DOB`}
              type="date"
              {...register(`travellers.${index}.DOB`, {
                required: "Please provide date of birth.",
              })}
            />
            <label className='input-label'>Date of Birth</label>
          </div>
        </div>
      </div>
    </div>
  );

  // Helper function to render collapsible section
  const renderPassengerSection = (
    title: string,
    passengers: Array<{passenger: Passenger, index: number}>,
    sectionKey: string,
    description: string
  ) => {
    if (passengers.length === 0) return null;

    return (
      <div className={styles["passenger-section"]}>
        <div
          className={styles["section-header"]}
          onClick={() => toggleSection(sectionKey)}
        >
          <div className={styles["section-title"]}>
            <h3>{title} ({passengers.length})</h3>
            <p className={styles["section-description"]}>{description}</p>
          </div>
          <div className={styles["expand-icon"]}>
            {expandedSections[sectionKey] ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </div>
        </div>

        {expandedSections[sectionKey] && (
          <div className={styles["section-content"]}>
            {passengers.map(({passenger, index}) => renderPassengerForm(passenger, index))}
          </div>
        )}
      </div>
    );
  };

  const onSubmit: SubmitHandler<travel_form> = async (data) => {
    // Validate form data before calling API
    if (!data.email || !data.phone) {
      alert('Please fill in email and phone number');
      return;
    }

    if (!data.travellers || data.travellers.length === 0) {
      alert('Please add traveller information');
      return;
    }

    // Check if all required traveller fields are filled
    for (let i = 0; i < data.travellers.length; i++) {
      const traveller = data.travellers[i];
      if (!traveller || !traveller.Title || !traveller.FName || !traveller.LName || !traveller.DOB) {
        alert(`Please fill in all required fields for ${traveller?.paxHead || `Traveller ${i + 1}`}`);
        return;
      }
    }

    try {
      setIsLoad(true);
      localStorage.setItem('TravelerForm', JSON.stringify(data));

      // Call the parent component's booking function
      handleMakePayment(data);
    } catch (error) {
      alert('An error occurred while processing your booking. Please try again.');
    } finally {
      setIsLoad(false);
    }
  }
  return (
    <div className={styles["flight-review-travell-form-div"]}>
      <form onSubmit={handleSubmit(onSubmit)}>
        {/* Passenger Information Section */}
        <section>
          <h2>Passenger Information</h2>
          <div className={styles["body-div-data"]}>
            <div className={styles["body-padding-div"]}>
              <div className={styles["passengers-container"]}>
                {/* Adults Section */}
                {renderPassengerSection(
                  "Adults",
                  groupedPassengers.adults,
                  "adults",
                  "Passengers 12 years and above"
                )}

                {/* Children Section */}
                {renderPassengerSection(
                  "Children",
                  groupedPassengers.children,
                  "children",
                  "Passengers between 2-11 years"
                )}

                {/* Infants Section */}
                {renderPassengerSection(
                  "Infants",
                  groupedPassengers.infants,
                  "infants",
                  "Passengers under 2 years"
                )}
              </div>
            </div>
          </div>

        </section >

        {/* Contact Details Section */}
        < section >
          <h2>Contact Details</h2>
          <div className={styles["body-div-data"]}>
            <div className={styles["body-padding-div"]}>
              <div className={styles["pax-travel-div"]}>
                  <div className={styles["form-input-div"]}>
                      <div className={styles["form-width-50"]}>
                        <div className="input-field">
                        <input
                            className='input-element'
                            id={`email`}
                            type="text"
                            placeholder="Type..."
                            {...register(`email`, {
                              required: "Please Enter Your Email.",
                            })}
                          />
                          <label className='input-label'>Email</label>
                        </div>
                      </div>
                      <div className={styles["form-width-50"]}>
                        <div className="input-field">
                        <input
                            className='input-element'
                            id={`phone`}
                            type="text"
                            placeholder="Type..."
                            {...register(`phone`, {
                              required: "Please Enter Your phone.",
                            })}
                          />
                          <label className='input-label'>Phone</label>
                        </div>
                      </div>
                  </div>
              </div>
            </div>
          </div>
        </section >

        <section>
              <div className={styles["payment-bttn-div"]}>
                <button className="dy_primary_bttn" type='submit'>
                  Make Payment
                </button>
              </div>
            </section>
      </form >

      {isLoad && (
            <Loader />
      )}
    </div >
  );
};

export default FlightReviewTravellerForm;
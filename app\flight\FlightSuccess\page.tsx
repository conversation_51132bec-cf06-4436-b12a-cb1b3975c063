"use client"

import PaymentLoader from "components/payment-loader/PaymentLoader"
import {
    Check<PERSON>ircle,
    PlaneTakeoff,
    PlaneLanding,
    CalendarDays,
    User2,
    Hash,
    Ticket,
    FileText,
    Home,
    Clock,
    Users,
    CreditCard
} from "lucide-react"
import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"

interface BookingDetails {
    bookingId: string
    bookingReference: string
    passengerName: string
    flightNumber: string
    departure: string
    arrival: string
    date: string
    amount: number
    status: string
    travellersCount: number
    departureTime: string
    arrivalTime: string
    airline: string
    duration: string
}

export default function FlightSuccessPage() {
    const router = useRouter();
    const [isLoad, setIsLoad] = useState<boolean>(true);
    const [booking, setBooking] = useState<BookingDetails>();
    const [hasBookingData, setHasBookingData] = useState<boolean>(false);
    const [isMobile, setIsMobile] = useState<boolean>(false);

    const accentColor = "#087767"; // Using app's primary color

    useEffect(() => {
        const timer = setTimeout(() => {
            setIsLoad(false)
        }, 3000);

        // Check if mobile
        const checkMobile = () => {
            setIsMobile(window.innerWidth <= 768);
        };
        checkMobile();
        window.addEventListener('resize', checkMobile);

        // Try to get real booking data first
        const latestBookingData = localStorage.getItem('dyLatestBooking');
        const travelerFormData = localStorage.getItem('TravelerForm');

        if (latestBookingData) {
            try {
                const bookingResponse: any = JSON.parse(latestBookingData);
                const travelerData: any = travelerFormData ? JSON.parse(travelerFormData) : {};

                // Extract flight information from the first trip
                const firstTrip = bookingResponse.FlightBooking?.Trips?.[0];
                const firstSegment = firstTrip?.Segments?.[0];
                const flight = firstSegment?.Flight;

                setBooking({
                    bookingId: bookingResponse.MasterBooking?.id?.toString() || "N/A",
                    bookingReference: bookingResponse.MasterBooking?.booking_reference || "N/A",
                    passengerName: (travelerData.travellers?.[0]?.FName + " " + travelerData.travellers?.[0]?.LName) || travelerData.email || "Passenger",
                    flightNumber: flight ? `${flight.MAC} ${flight.FlightNo}` : "N/A",
                    departure: flight?.DepAirportName || "N/A",
                    arrival: flight?.ArrAirportName || "N/A",
                    date: flight?.DepartureTime ? new Date(flight.DepartureTime).toLocaleDateString() : "N/A",
                    amount: bookingResponse.FlightBooking?.NetAmount || 0,
                    status: bookingResponse.MasterBooking?.status || "confirmed",
                    travellersCount: bookingResponse.FlightBooking?.Travellers?.length || 1,
                    departureTime: flight?.DepartureTime ? new Date(flight.DepartureTime).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : "N/A",
                    arrivalTime: flight?.ArrivalTime ? new Date(flight.ArrivalTime).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) : "N/A",
                    airline: flight?.Airline || "N/A",
                    duration: flight?.Duration || "N/A"
                });
                setHasBookingData(true);
            } catch (error) {
                console.error('Error parsing booking data:', error);
                setFallbackBookingData(travelerFormData);
            }
        } else {
            setFallbackBookingData(travelerFormData);
        }

        return () => {
            clearTimeout(timer);
            window.removeEventListener('resize', checkMobile);
        };
    }, []);

    const setFallbackBookingData = (travelerFormData: string | null) => {
        const data: any = travelerFormData ? JSON.parse(travelerFormData) : {};
        setBooking({
            bookingId: "ABC12345",
            bookingReference: "REF-" + Date.now(),
            passengerName: data.email || '<EMAIL>',
            flightNumber: "AI-202",
            departure: "Kochi (COK)",
            arrival: "New Delhi (DEL)",
            date: "2025-03-21",
            amount: 9646,
            status: "confirmed",
            travellersCount: 1,
            departureTime: "14:30",
            arrivalTime: "17:15",
            airline: "Air India",
            duration: "2h 45m"
        });
        setHasBookingData(false);
    };

    const handleViewItinerary = () => {
        console.log('View Itinerary clicked:', { hasBookingData, bookingId: booking?.bookingId });

        if (hasBookingData && booking?.bookingReference && booking.bookingReference !== "N/A") {
            console.log('Navigating to itinerary page with ID:', booking.bookingReference);
            router.push(`/flight/bookings/itinerary/${booking.bookingReference}`);
        } else {
            console.log('No valid booking data, redirecting to bookings list');
            // If no real booking data, redirect to bookings page
            router.push('/flight/bookings');
        }
    };

    const handleBackToHome = () => {
        router.push('/flight/FlightHomePage');
    };

    return (
        <>
            <div style={{ backgroundColor: '#e4f0f0', minHeight: '100vh', padding: '20px 0' }}>
                {/* Header Section */}
                {/* <div style={{ backgroundColor: '#f5f5f5', padding: '10px 0', marginBottom: '20px' }}>
                    <div className="container">
                        <h1 style={{ fontSize: '18px', lineHeight: '18px', marginBottom: '0', color: '#000000' }}>
                            Booking Confirmation
                        </h1>
                    </div>
                </div> */}

                <div className="container">
                    {/* Success Card */}
                    <div style={{
                        backgroundColor: 'white',
                        borderRadius: '7px',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                        border: '1px solid #ebebeb',
                        overflow: 'hidden'
                    }}>
                        {/* Success Header */}
                        <div style={{
                            backgroundColor: '#087767',
                            color: 'white',
                            padding: '30px',
                            textAlign: 'center'
                        }}>
                            <CheckCircle style={{
                                width: '60px',
                                height: '60px',
                                margin: '0 auto 15px auto',
                                display: 'block'
                            }} />
                            <h1 style={{
                                fontSize: '28px',
                                fontWeight: '600',
                                marginBottom: '8px',
                                margin: '0 0 8px 0'
                            }}>
                                Booking Confirmed!
                            </h1>
                            <p style={{
                                fontSize: '16px',
                                margin: '0',
                                opacity: '0.9'
                            }}>
                                Your flight has been successfully booked
                            </p>
                        </div>

                        {/* Booking Details Section */}
                        <div style={{ padding: isMobile ? '20px' : '30px' }}>
                            {/* Booking Information - Full Width */}
                            <div style={{ marginBottom: '30px' }}>
                                <h2 style={{
                                    fontSize: '19px',
                                    fontWeight: '500',
                                    color: '#000',
                                    marginBottom: '20px'
                                }}>
                                    Booking Information
                                </h2>

                                <div style={{
                                    backgroundColor: '#ecfffc',
                                    borderRadius: '7px',
                                    padding: '20px',
                                    border: '1px solid #d0d0d0'
                                }}>
                                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '15px' }}>
                                            <Hash style={{ color: accentColor, width: '20px', height: '20px' }} />
                                            <div>
                                                <p style={{ fontSize: '13px', color: '#525252', margin: '0 0 3px 0' }}>Booking ID</p>
                                                <p style={{ fontSize: '15px', fontWeight: '600', color: '#000', margin: '0' }}>{booking?.bookingId}</p>
                                            </div>
                                        </div>

                                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '15px' }}>
                                            <Ticket style={{ color: accentColor, width: '20px', height: '20px' }} />
                                            <div>
                                                <p style={{ fontSize: '13px', color: '#525252', margin: '0 0 3px 0' }}>Booking Reference</p>
                                                <p style={{ fontSize: '15px', fontWeight: '600', color: '#000', margin: '0' }}>{booking?.bookingReference}</p>
                                            </div>
                                        </div>

                                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '15px' }}>
                                            <User2 style={{ color: accentColor, width: '20px', height: '20px' }} />
                                            <div>
                                                <p style={{ fontSize: '13px', color: '#525252', margin: '0 0 3px 0' }}>Passenger</p>
                                                <p style={{ fontSize: '15px', fontWeight: '600', color: '#000', margin: '0' }}>{booking?.passengerName}</p>
                                            </div>
                                        </div>

                                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '15px' }}>
                                            <Users style={{ color: accentColor, width: '20px', height: '20px' }} />
                                            <div>
                                                <p style={{ fontSize: '13px', color: '#525252', margin: '0 0 3px 0' }}>Travellers</p>
                                                <p style={{ fontSize: '15px', fontWeight: '600', color: '#000', margin: '0' }}>{booking?.travellersCount} Passenger(s)</p>
                                            </div>
                                        </div>

                                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '0' }}>
                                            <CreditCard style={{ color: accentColor, width: '20px', height: '20px' }} />
                                            <div>
                                                <p style={{ fontSize: '13px', color: '#525252', margin: '0 0 3px 0' }}>Total Amount</p>
                                                <p style={{ fontSize: '15px', fontWeight: '600', color: '#000', margin: '0' }}>₹{booking?.amount?.toLocaleString()}</p>
                                            </div>
                                        </div>
                                    </div>
                            </div>

                            {/* Flight Details - Full Width */}
                            <div style={{ marginBottom: '30px' }}>
                                <h2 style={{
                                    fontSize: '19px',
                                    fontWeight: '500',
                                    color: '#000',
                                    marginBottom: '20px'
                                }}>
                                    Flight Details
                                </h2>

                                <div style={{
                                    backgroundColor: '#f1f7ff',
                                    borderRadius: '7px',
                                    padding: '20px',
                                    border: '1px solid #d0d0d0'
                                }}>
                                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '15px' }}>
                                            <span style={{ color: accentColor, fontSize: '24px', fontWeight: 'bold' }}>✈</span>
                                            <div>
                                                <p style={{ fontSize: '13px', color: '#525252', margin: '0 0 3px 0' }}>Flight</p>
                                                <p style={{ fontSize: '15px', fontWeight: '600', color: '#000', margin: '0 0 2px 0' }}>{booking?.flightNumber}</p>
                                                <p style={{ fontSize: '13px', color: '#525252', margin: '0' }}>{booking?.airline}</p>
                                            </div>
                                        </div>

                                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '15px' }}>
                                            <PlaneTakeoff style={{ color: accentColor, width: '20px', height: '20px' }} />
                                            <div>
                                                <p style={{ fontSize: '13px', color: '#525252', margin: '0 0 3px 0' }}>Departure</p>
                                                <p style={{ fontSize: '15px', fontWeight: '600', color: '#000', margin: '0 0 2px 0' }}>{booking?.departure}</p>
                                                <p style={{ fontSize: '13px', color: '#525252', margin: '0' }}>{booking?.departureTime}</p>
                                            </div>
                                        </div>

                                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '15px' }}>
                                            <PlaneLanding style={{ color: accentColor, width: '20px', height: '20px' }} />
                                            <div>
                                                <p style={{ fontSize: '13px', color: '#525252', margin: '0 0 3px 0' }}>Arrival</p>
                                                <p style={{ fontSize: '15px', fontWeight: '600', color: '#000', margin: '0 0 2px 0' }}>{booking?.arrival}</p>
                                                <p style={{ fontSize: '13px', color: '#525252', margin: '0' }}>{booking?.arrivalTime}</p>
                                            </div>
                                        </div>

                                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '15px' }}>
                                            <CalendarDays style={{ color: accentColor, width: '20px', height: '20px' }} />
                                            <div>
                                                <p style={{ fontSize: '13px', color: '#525252', margin: '0 0 3px 0' }}>Travel Date</p>
                                                <p style={{ fontSize: '15px', fontWeight: '600', color: '#000', margin: '0' }}>{booking?.date}</p>
                                            </div>
                                        </div>

                                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '0' }}>
                                            <Clock style={{ color: accentColor, width: '20px', height: '20px' }} />
                                            <div>
                                                <p style={{ fontSize: '13px', color: '#525252', margin: '0 0 3px 0' }}>Duration</p>
                                                <p style={{ fontSize: '15px', fontWeight: '600', color: '#000', margin: '0' }}>{booking?.duration}</p>
                                            </div>
                                        </div>
                                </div>
                            </div>

                            {/* Action Buttons */}
                            <div style={{
                                marginTop: '30px',
                                display: 'flex',
                                flexDirection: isMobile ? 'column' : 'row',
                                gap: '15px',
                                justifyContent: 'center',
                                alignItems: 'center'
                            }}>
                                <button
                                    onClick={handleViewItinerary}
                                    className="dy_primary_bttn"
                                    style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '8px',
                                        fontSize: '15px',
                                        padding: '12px 24px',
                                        width: isMobile ? '100%' : 'auto',
                                        justifyContent: 'center'
                                    }}
                                >
                                    <FileText style={{ width: '18px', height: '18px' }} />
                                    {hasBookingData && booking?.bookingReference && booking.bookingReference !== "N/A"
                                        ? "View Itinerary"
                                        : "View My Bookings"}
                                </button>

                                <button
                                    onClick={handleBackToHome}
                                    className="dy_secondary_bttn"
                                    style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '8px',
                                        fontSize: '15px',
                                        padding: '12px 24px',
                                        width: isMobile ? '100%' : 'auto',
                                        justifyContent: 'center'
                                    }}
                                >
                                    <Home style={{ width: '18px', height: '18px' }} />
                                    Back to Home
                                </button>
                            </div>

                            {/* Status Badge */}
                            {/* <div style={{ marginTop: '25px', textAlign: 'center' }}>
                                <span style={{
                                    display: 'inline-flex',
                                    alignItems: 'center',
                                    padding: '8px 16px',
                                    borderRadius: '20px',
                                    fontSize: '14px',
                                    fontWeight: '500',
                                    backgroundColor: '#ecfffc',
                                    color: '#087767',
                                    border: '1px solid #087767'
                                }}>
                                    <div style={{
                                        width: '8px',
                                        height: '8px',
                                        backgroundColor: '#008000',
                                        borderRadius: '50%',
                                        marginRight: '8px'
                                    }}></div>
                                    Status: {booking?.status ? booking.status.charAt(0).toUpperCase() + booking.status.slice(1) : 'Confirmed'}
                                </span>
                            </div> */}

                            {/* Important Note */}
                            <div style={{
                                marginTop: '15px',
                                backgroundColor: '#fff9f2',
                                border: '1px solid #d0d0d0',
                                borderRadius: '7px',
                                padding: '15px'
                            }}>
                                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
                                    <div style={{ fontSize: '18px', marginTop: '2px' }}>⚠️</div>
                                    <div>
                                        <h3 style={{
                                            fontSize: '16px',
                                            fontWeight: '600',
                                            color: '#b84a01',
                                            margin: '0 0 8px 0'
                                        }}>
                                            Important Information
                                        </h3>
                                        <p style={{
                                            fontSize: '14px',
                                            color: '#525252',
                                            margin: '0',
                                            lineHeight: '1.4'
                                        }}>
                                            Please arrive at the airport at least 2 hours before domestic flights and 3 hours before international flights.
                                            Keep your booking reference handy for check-in.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {isLoad && (
                <PaymentLoader />
            )}
        </>
    )
}
